import {
  Project,
  User,
  UserRole,
  ProjectStatus,
  SupervisorSubTask,
  SupervisorStatus,
  canModifyTask,
  isManager,
} from "@/types/project";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { formatDate } from "@/lib/datetime";
import { Progress } from "@/components/ui/progress";
import { StatusBadge } from "@/components/StatusBadge";
import { statusToTone, shouldHideActionButtons } from "@/lib/status";
import { Calendar, User as UserIcon, Eye, ArrowRight } from "lucide-react";
import { SupervisorAssignDialog } from "@/components/SupervisorAssignDialog";
import { SupervisorProgressDialog } from "@/components/SupervisorProgressDialog";
import { useState } from "react";

interface SupervisorProjectCardProps {
  project: Project;
  userRole: UserRole;
  currentUserId: string;
  onStatusUpdate: (
    projectId: string,
    newStatus: ProjectStatus,
    newSubStatus?: undefined,
    phaseCompletedAt?: string,
    phaseKey?: string
  ) => void;
  onViewDetails: (project: Project) => void;
  onAssignTask?: (projectId: string, assigneeId: string, phases?: string[]) => void;
  onDeleteProject?: (projectId: string) => void;
  availableUsers?: User[];
}

export const SupervisorProjectCard = ({
  project,
  userRole,
  currentUserId,
  onStatusUpdate,
  onViewDetails,
  onAssignTask,
  onDeleteProject, // kept in props signature for compatibility
  availableUsers = [],
}: SupervisorProjectCardProps) => {
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [showProgressDialog, setShowProgressDialog] = useState(false);

  const canModify = canModifyTask(userRole, project.assignedTo || "", currentUserId);
  const supervisorStatuses: ProjectStatus[] = ["supervisor_pending_assign", "inprogress", "completed"];

  const legacySupervisorSubtasks = [
    "floor_protection",
    "plaster_ceiling",
    "spc",
    "first_painting",
    "carpentry_measure",
    "measure_others",
    "carpentry_install",
    "quartz_measure",
    "quartz_install",
    "glass_measure",
    "glass_install",
    "final_wiring",
    "final_painting",
    "install_others",
    "plumbing",
    "cleaning",
    "defects",
  ] as const;

  const isSupervisorCardActive =
    (project.status === "inprogress" ||
      (Array.isArray(project.supervisorSelectedPhases) && project.supervisorSelectedPhases.length > 0) ||
      (legacySupervisorSubtasks as readonly string[]).includes(project.status as string)) &&
    project.status !== "supervisor_pending_assign" &&
    project.status !== "completed";

  // Managers can always assign
  const canAssignTask = isManager(userRole);

  // use shared StatusBadge for colors

  const getProgressPercentage = (status: ProjectStatus): number => {
    const selected = project.supervisorSelectedPhases || [];
    const phaseDates = project.supervisorPhaseDates || {};

    // If phases were selected, progress is based only on completed selected phases
    if (selected.length > 0) {
      const completedCount = selected.filter((phase) => !!(phaseDates as Record<string, string>)[phase]).length;
      const total = selected.length;
      return total > 0 ? (completedCount / total) * 100 : 0;
    }

    // No selected phases yet: show 0% while pending/in progress; 100% only if completed
    if (status === "completed") return 100;
    return 0;
  };

  const progressPercentage = getProgressPercentage(project.status);

  const handleSupervisorAssign = (assigneeId: string, phases: string[]) => {
    if (onAssignTask) onAssignTask(project.id, assigneeId, phases);
  };

  const handleSupervisorProgress = (selectedPhase: SupervisorSubTask, completionDate: string) => {
    onStatusUpdate(project.id, project.status, undefined, completionDate, selectedPhase);
  };

  const isSupervisorPendingAssign = project.status === "supervisor_pending_assign";
  const hideProgressForPendingAssign =
    (isSupervisorPendingAssign && !project.assignedTo) ||
    (((supervisorStatuses.includes(project.status) || (legacySupervisorSubtasks as readonly string[]).includes(project.status as string)) &&
      !project.assignedTo));
  const isMgr = isManager(userRole);
  // Hide progress button if pending assign (and not manager) OR when supervisor task is completed
  const isSupervisorCompleted = project.status === 'completed';
  const shouldHideProgress = (!isMgr && hideProgressForPendingAssign) || isSupervisorCompleted;

  return (
    <Card className="hover:shadow-md transition-shadow flex flex-col h-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold line-clamp-2">{project.title}</CardTitle>
          <div className="flex flex-col items-end gap-1">
            <StatusBadge status={project.status} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 flex flex-col flex-1">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <UserIcon className="h-4 w-4" />
            <span>{project.client}</span>
          </div>

          {project.assignedTo ? (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <UserIcon className="h-4 w-4" />
              <span>
                Assigned to: {
                  availableUsers?.find((u) => u.id === project.assignedTo)?.name || "Unknown"
                }
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-sm text-orange-600">
              <UserIcon className="h-4 w-4" />
              <span>Unassigned</span>
            </div>
          )}

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Created: {formatDate(project.createdAt)}</span>
          </div>

          {project.remarks && (
            <div className="text-sm">
              <span className="text-muted-foreground">Remarks: </span>
              <span
                className="text-foreground line-clamp-2 max-w-full overflow-hidden text-ellipsis"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  maxHeight: '2.5rem'
                }}
                title={project.remarks}
              >
                {project.remarks}
              </span>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} tone={statusToTone(project.status)} className="h-2" />
        </div>

        <div className="flex-1" />

        <div className="flex gap-2 pt-2 mt-auto">
          <Button variant="outline" size="sm" onClick={() => onViewDetails(project)} className="flex-1">
            <Eye className="h-4 w-4 mr-1" />
            Details
          </Button>

          {/* Assign button visible on card when unassigned (manager only), same as Designer card */}
          {!shouldHideActionButtons(project.status) && isManager(userRole) && !project.assignedTo && (
            <Button variant="default" size="sm" onClick={() => setShowAssignDialog(true)} className="flex-1">
              <UserIcon className="h-4 w-4 mr-1" />
              Assign
            </Button>
          )}

          {/* Keep Complete Subtask on the card */}
          {!shouldHideActionButtons(project.status) && (canModify || isManager(userRole)) && !shouldHideProgress && (
            <Button size="sm" onClick={() => setShowProgressDialog(true)} className="flex-1">
              <ArrowRight className="h-4 w-4 mr-1" />
              Complete Subtask
            </Button>
          )}
        </div>
      </CardContent>

      <SupervisorProgressDialog
        open={showProgressDialog}
        onOpenChange={setShowProgressDialog}
        currentStatus={project.status as SupervisorStatus}
        supervisorSelectedPhases={project.supervisorSelectedPhases}
        supervisorPhaseDates={project.supervisorPhaseDates}
        onConfirm={handleSupervisorProgress}
      />

      {(userRole === "manager") && (
        <SupervisorAssignDialog
          open={showAssignDialog}
          onOpenChange={setShowAssignDialog}
          designers={availableUsers?.filter((u) => u.role === "supervisor") || []}
          onConfirm={handleSupervisorAssign}
        />
      )}
    </Card>
  );
};

