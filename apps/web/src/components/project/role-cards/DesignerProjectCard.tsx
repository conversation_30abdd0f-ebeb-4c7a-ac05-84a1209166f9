import {
  Project,
  UserRole,
  ProjectStatus,
  DesignerStatus,
  getStatusesForRole,
  canModifyTask,
  isManager,
} from "@/types/project";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

import { Progress } from "@/components/ui/progress";
import { StatusBadge } from "@/components/StatusBadge";
import { statusToTone, shouldHideActionButtons } from "@/lib/status";
import { formatDate, formatDateTime } from "@/lib/datetime";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, User as UserIcon, Eye, ArrowRight, RotateCcw, Clock, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { useState } from "react";

interface DesignerProjectCardProps {
  project: Project;
  userRole: UserRole;
  currentUserId: string;
  onStatusUpdate: (projectId: string, newStatus: ProjectStatus) => void;
  onViewDetails: (project: Project) => void;
  onRevisionRequest?: (projectId: string) => void;
  onAssignTask?: (projectId: string, assigneeId: string) => void;
  onDeleteProject?: (projectId: string) => void;
  availableUsers?: { id: string; name: string; role: UserRole }[];
}

export const DesignerProjectCard = ({
  project,
  userRole,
  currentUserId,
  onStatusUpdate,
  onViewDetails,
  onRevisionRequest,
  onAssignTask,
  onDeleteProject,
  availableUsers = [],
}: DesignerProjectCardProps) => {
  const [showRevisionDialog, setShowRevisionDialog] = useState(false);
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [selectedAssignee, setSelectedAssignee] = useState("");

  // use shared status tone mapping

  const getProgressPercentage = (status: ProjectStatus): number => {
    const designerStatuses: ProjectStatus[] = [
      "designer_pending_assign",
      "checklist",
      "3d",
      "2d",
      "furniture_list",
      "complete",
    ];
    const currentIndex = designerStatuses.indexOf(status);
    return currentIndex >= 0 ? ((currentIndex + 1) / designerStatuses.length) * 100 : 0;
  };



  const canModify = canModifyTask(userRole, project.assignedTo || "", currentUserId);

  const isDesignerPendingAssign = project.status === "designer_pending_assign";
  // Managers can assign only when unassigned on the card (reassign lives in Details)
  const canAssignTask = isManager(userRole) && !project.assignedTo;
  const hideProgressForPendingAssign = isDesignerPendingAssign || !project.assignedTo;

  const canShowRevision =
    (project.status === "3d" || project.status === "2d") && canModify;

  const getNextStatus = (status: ProjectStatus): ProjectStatus | null => {
    const roleStatuses = getStatusesForRole("designer");
    const currentIndex = roleStatuses.indexOf(status);
    if (currentIndex < 0) return null;
    if (currentIndex < roleStatuses.length - 1) return roleStatuses[currentIndex + 1];
    return null;
  };

  const handleProgressClick = () => {
    const next = getNextStatus(project.status);
    if (next) onStatusUpdate(project.id, next);
  };

  const handleAssignTask = () => {
    if (selectedAssignee && onAssignTask) {
      onAssignTask(project.id, selectedAssignee);
      setShowAssignDialog(false);
      setSelectedAssignee("");
    }
  };

  const handleRevisionRequest = () => setShowRevisionDialog(true);

  const confirmRevisionRequest = () => {
    if (onRevisionRequest) {
      onRevisionRequest(project.id);
      toast.success("Revision requested successfully");
    }
    setShowRevisionDialog(false);
  };

  const getCurrentRevisions = () => {
    if (project.status === "3d") return project.revisions3d || [];
    if (project.status === "2d") return project.revisions2d || [];
    return [];
  };

  const currentRevisions = getCurrentRevisions();
  const revisionCount = currentRevisions.length;
  const hasExcessiveRevisions = revisionCount > 3;

  // Check if task is overdue
  const isOverdue = project.dueDate && new Date(project.dueDate) < new Date();

  const getDisplayDate = () => {
    if (currentRevisions.length > 0) {
      const latestRevision = currentRevisions[currentRevisions.length - 1];
      return formatDate(latestRevision);
    }
    return formatDate(project.createdAt);
  };

  const getDueDateDisplay = () => {
    if (!project.dueDate || (project.status !== "3d" && project.status !== "2d")) return null;
    const dueDate = new Date(project.dueDate);
    const today = new Date();
    const diffTime = dueDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    let badgeColor = "bg-muted text-muted-foreground";
    if (diffDays < 0) badgeColor = "bg-destructive text-destructive-foreground";
    else if (diffDays <= 2) badgeColor = "bg-orange-500 text-white";
    else if (diffDays <= 5) badgeColor = "bg-yellow-500 text-black";

    return (
      <Badge className={`${badgeColor} text-xs`}>
        <Clock className="h-3 w-3 mr-1" />
        {diffDays < 0 ? `${Math.abs(diffDays)} days overdue` : diffDays === 0 ? "Due today" : `${diffDays} days left`}
      </Badge>
    );
  };

  const progressPercentage = getProgressPercentage(project.status);

  // Determine card border color based on priority
  const getCardBorderClass = () => {
    if (isOverdue) return "border-red-500 border-2";
    if (hasExcessiveRevisions) return "border-orange-500 border-2";
    return "";
  };

  return (
    <Card className={`hover:shadow-md transition-shadow flex flex-col h-full ${getCardBorderClass()}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold line-clamp-2">{project.title}</CardTitle>
          <div className="flex flex-col items-end gap-1">
            <StatusBadge status={project.status} />
            {revisionCount > 0 && (
              <Badge
                variant={isOverdue ? "destructive" : hasExcessiveRevisions ? "outline" : "secondary"}
                className={`text-xs ${
                  isOverdue
                    ? ""
                    : hasExcessiveRevisions
                      ? "bg-orange-50 border-orange-200 text-orange-700"
                      : ""
                }`}
              >
                {project.status === "3d" ? "3D" : "2D"}: {revisionCount} revision{revisionCount > 1 ? "s" : ""}
                {(isOverdue || hasExcessiveRevisions) && " ⚠️"}
              </Badge>
            )}
            {getDueDateDisplay()}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 flex flex-col flex-1">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <UserIcon className="h-4 w-4" />
            <span>{project.client}</span>
          </div>

          {project.assignedTo ? (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <UserIcon className="h-4 w-4" />
              <span>Assigned to: {availableUsers.find((u) => u.id === project.assignedTo)?.name || "Unknown"}</span>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-sm text-orange-600">
              <UserIcon className="h-4 w-4" />
              <span>Unassigned</span>
            </div>
          )}

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>{revisionCount > 0 ? `Last revision: ${getDisplayDate()}` : `Created: ${getDisplayDate()}`}</span>
          </div>

          {project.remarks && (
            <div className="text-sm">
              <span className="text-muted-foreground">Remarks: </span>
              <span
                className="text-foreground line-clamp-2 max-w-full overflow-hidden text-ellipsis"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  maxHeight: '2.5rem'
                }}
                title={project.remarks}
              >
                {project.remarks}
              </span>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} tone={statusToTone(project.status)} className="h-2" />
        </div>

        <div className="flex-1"></div>

        <div className="flex gap-2 pt-2 mt-auto">
          <Button variant="outline" size="sm" onClick={() => onViewDetails(project)} className="flex-1">
            <Eye className="h-4 w-4 mr-1" />
            Details
          </Button>

          {canShowRevision && (
            <AlertDialog open={showRevisionDialog} onOpenChange={setShowRevisionDialog}>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="sm" className="flex-1">
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Revision
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Request Revision</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to request a revision for this {project.status.toUpperCase()} design?
                    This will update the due date based on revision timeline and add a revision record.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={confirmRevisionRequest}>Confirm Revision</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}

          {!shouldHideActionButtons(project.status) && canAssignTask && (
            <AlertDialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
              <AlertDialogTrigger asChild>
                <Button variant="default" size="sm" className="flex-1">
                  <UserIcon className="h-4 w-4 mr-1" />
                  Assign
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Assign Task</AlertDialogTitle>
                  <AlertDialogDescription>Select a designer to assign this task to:</AlertDialogDescription>
                </AlertDialogHeader>
                <div className="py-4">
                  <Select value={selectedAssignee} onValueChange={setSelectedAssignee}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a designer" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableUsers
                        .filter((u) => u.role === "designer")
                        .map((designer) => (
                          <SelectItem key={designer.id} value={designer.id}>
                            {designer.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={() => setSelectedAssignee("")}>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleAssignTask} disabled={!selectedAssignee}>
                    Assign Task
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}

          {!shouldHideActionButtons(project.status) && canModify && !hideProgressForPendingAssign && getNextStatus(project.status) && (
            <Button size="sm" onClick={handleProgressClick} className="flex-1">
              <ArrowRight className="h-4 w-4 mr-1" />
              Progress
            </Button>
          )}

          {/* Delete moved to Details panel; keep card clean like Sales */}
        </div>
      </CardContent>
    </Card>
  );
};

