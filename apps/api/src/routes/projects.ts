import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { calculateDueDate } from '../lib/dueDate';
import { randomUUID } from 'crypto';

const router = Router();
const prisma = new PrismaClient();

// Simple role helper using header. Default to internal if missing.
import type { Request } from 'express';
const isCustomer = (req: Request) => String(req.headers['x-user-role'] || '').toLowerCase() === 'customer';

// Helper: infer roles based on parentTaskId and status groupings
const isSalesTask = (p: { parentTaskId: string | null }): boolean => !p.parentTaskId;
const isDesignerTask = (p: { parentTaskId: string | null; status: string }): boolean => !!p.parentTaskId && ['designer_pending_assign','checklist','3d','2d','furniture_list','complete','designer_lost_deal'].includes(p.status);
const isSupervisorTask = (p: { status: string }): boolean => ['supervisor_pending_assign','inprogress','completed','supervisor_lost_deal'].includes(p.status);

// Minimal event emitter (safe if TaskEvent table doesn't exist)
const emitEvent = async (data: {
  caseId: string;
  taskId: string;
  role: 'sales' | 'designer' | 'supervisor';
  type: string;
  actorUserId?: string | null;
  source?: string | null;
  payload?: unknown;
  occurredAt?: Date | string;
}) => {
  try {
    const id = randomUUID();
    const occurredAt = data.occurredAt ? new Date(data.occurredAt) : new Date();
    const payload = data.payload ? JSON.stringify(data.payload) : null;
    await prisma.$executeRawUnsafe(
      'INSERT INTO "TaskEvent" ("id","caseId","taskId","role","type","occurredAt","actorUserId","source","payload") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9)',
      id,
      data.caseId,
      data.taskId,
      data.role,
      data.type,
      occurredAt,
      data.actorUserId ?? null,
      data.source ?? 'api',
      payload,
    );
  } catch (e) {
    // Swallow errors to avoid breaking primary flow if table doesn't exist
  }
};

// Supervisor status list for guards
const SUPERVISOR_STATUSES = [
  'supervisor_pending_assign', 'inprogress', 'completed'
] as const;





const createProjectSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  client: z.string().min(1, 'Client is required'),
  salesAmount: z.number().int().nonnegative({ message: 'Sales amount must be a non-negative integer' }),
  assignedTo: z.string().min(1, 'AssignedTo is required'),
});

const updateStatusSchema = z.object({
  status: z.string(),
  salesSubStatus: z.string().optional(),
  isRevision: z.boolean().optional(),
  phaseCompletedAt: z.string().optional(), // ISO date string for supervisor subtask completion
  phaseKey: z.string().optional(), // which subtask is being completed
  isPhaseCompletion: z.boolean().optional(), // Flag to indicate this is just a phase completion, not status change
});

const updateProjectSchema = z.object({
  title: z.string().optional(),
  client: z.string().optional(),
  assignedTo: z.string().optional(),
  expiredDate: z.string().optional(),
  dueDate: z.string().optional(),
  supervisorSelectedPhases: z.array(z.string()).optional(),
  supervisorPhaseDates: z.record(z.string(), z.string()).optional(),
  remarks: z.string().optional(),
});

router.get('/', async (_req, res) => {
  const projects = await prisma.project.findMany({ orderBy: { createdAt: 'desc' } });

  if (isCustomer(_req)) {
    // Mask sensitive fields for customers
    const masked = projects.map(p => ({ id: p.id, title: p.title, client: p.client, status: p.status, createdAt: p.createdAt, updatedAt: p.updatedAt }));
    return res.json(masked);
  }

  res.json(projects);
});

router.get('/:id', async (req, res) => {
  const { id } = req.params;
  const project = await prisma.project.findUnique({ where: { id } });
  if (!project) return res.status(404).json({ error: 'Not found' });

  if (isCustomer(req)) {
    const { id: pid, title, client, status, createdAt, updatedAt } = project;
    return res.json({ id: pid, title, client, status, createdAt, updatedAt });
  }

  res.json(project);
});

// Separate assignment endpoints
const assignDesignerSchema = z.object({
  assignedTo: z.string(),
});

router.patch('/:id/assign/designer', async (req, res) => {
  const { id } = req.params;
  const parse = assignDesignerSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const current = await prisma.project.findUnique({ where: { id } });
  if (!current) return res.status(404).json({ error: 'Not found' });

  // Managers can assign any time; non-managers follow status restriction
  const role = String(req.headers['x-user-role'] || '').toLowerCase();
  const isManager = role === 'manager';
  if (!isManager) {
    // Only allow when status is designer_pending_assign (designer flow uses this endpoint)
    if (current.status !== 'designer_pending_assign') {
      // If this is a freshly created designer task via auto-flow and client is a bit out of sync,
      // fallback: allow assignment when current status is 'checklist' but no assignee yet
      if (!(current.status === 'checklist' && (!current.assignedTo || current.assignedTo === ''))) {
        return res.status(400).json({ error: 'Designer assignment allowed only in designer_pending_assign tasks' });
      }
    }
  }

  // Move to checklist if still at designer_pending_assign; otherwise keep at checklist
  const newStatus = current.status === 'designer_pending_assign' ? 'checklist' : current.status;

  const updated = await prisma.project.update({
    where: { id },
    data: { assignedTo: parse.data.assignedTo, status: newStatus },
  });

  // Emit assignment change for Designer
  const actor = (req.headers['x-user-id'] as string) || null;
  try {
    await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'designer', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
  } catch {}

  res.json(updated);
});

const assignSupervisorSchema = z.object({
  assignedTo: z.string(),
  selectedTasks: z.array(z.string()).min(1),
});

router.patch('/:id/assign/supervisor', async (req, res) => {
  const { id } = req.params;
  const parse = assignSupervisorSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const current = await prisma.project.findUnique({ where: { id } });
  if (!current) return res.status(404).json({ error: 'Not found' });

  const role = String(req.headers['x-user-role'] || '').toLowerCase();
  const isManager = role === 'manager';

  const isSupervisorPendingAssign = current.status === 'supervisor_pending_assign';
  const isSupervisorTaskUnassigned = SUPERVISOR_STATUSES.includes(current.status as (typeof SUPERVISOR_STATUSES)[number]) && current.status !== 'supervisor_pending_assign' && (!current.assignedTo || current.assignedTo === '');

  if (!isManager && !isSupervisorPendingAssign && !isSupervisorTaskUnassigned) {
    return res.status(400).json({ error: 'Supervisor assignment allowed only in supervisor_pending_assign or unassigned supervisor tasks' });
  }

  const data: { assignedTo: string; status?: string; supervisorSelectedPhases: string[]; supervisorPhaseDates: Record<string, string> } = {
    assignedTo: parse.data.assignedTo,
    supervisorSelectedPhases: parse.data.selectedTasks,
    supervisorPhaseDates: {},
  };
  if (isSupervisorPendingAssign) {
    data.status = 'inprogress'; // On assignment, supervisor status becomes inprogress
  }

  const updated = await prisma.project.update({ where: { id }, data });

  // Emit supervisor assignment + selection events
  const actor = (req.headers['x-user-id'] as string) || null;
  try {
    await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'supervisor', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
  } catch {
    // ignore event errors
  }
  try {
    await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'supervisor', type: 'supervisor_task_assigned', actorUserId: actor, source: 'api', payload: { selected: updated.supervisorSelectedPhases || [] } });
  } catch {
    // ignore event errors
  }

  res.json(updated);
});


router.post('/', async (req, res) => {
  const parse = createProjectSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const p = parse.data;
  const rootId = randomUUID();
  const created = await prisma.project.create({ data: { id: rootId, caseId: rootId, ...p } });

  // Emit task_created for Sales root
  const actor = (req.headers['x-user-id'] as string) || null;
  await emitEvent({ caseId: created.caseId, taskId: created.id, role: 'sales', type: 'task_created', actorUserId: actor, source: 'api', payload: { status: created.status } });

  res.status(201).json(created);
});

router.patch('/:id', async (req, res) => {
  const { id } = req.params;
  const parse = updateProjectSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const current = await prisma.project.findUnique({ where: { id } });
  if (!current) return res.status(404).json({ error: 'Not found' });

  const cast = parse.data as { title?: string; client?: string; assignedTo?: string; expiredDate?: string; dueDate?: string; supervisorSelectedPhases?: string[]; supervisorPhaseDates?: Record<string, string> };
  const data: { title?: string; client?: string; assignedTo?: string; expiredDate?: Date; dueDate?: Date; supervisorSelectedPhases?: string[]; supervisorPhaseDates?: Record<string, string>; status?: string } = {
    title: cast.title,
    client: cast.client,
    assignedTo: cast.assignedTo,
    supervisorSelectedPhases: cast.supervisorSelectedPhases,
    supervisorPhaseDates: cast.supervisorPhaseDates,
    expiredDate: cast.expiredDate ? new Date(cast.expiredDate) : undefined,
    dueDate: cast.dueDate ? new Date(cast.dueDate) : undefined,
  };

  // Assignment rules:
  // - Designer: only when status === 'pending_assign' (auto-progress to 'checklist')
  // - Supervisor: only when status === 'pending_assign' (auto-progress to 'floor_protection') or unassigned supervisor task
  if (data.assignedTo) {
    const isDesignerPendingAssign = current.status === 'designer_pending_assign';
    const wantsSupervisorAssign = Array.isArray(data.supervisorSelectedPhases) && data.supervisorSelectedPhases.length > 0;

    // Disambiguate designer vs supervisor by explicit statuses
    const isDesignerAssign = isDesignerPendingAssign && !wantsSupervisorAssign;
    const isSupervisorPendingAssign = current.status === 'supervisor_pending_assign' && wantsSupervisorAssign;

    const isSupervisorTask = SUPERVISOR_STATUSES.includes(current.status as (typeof SUPERVISOR_STATUSES)[number]) && current.status !== 'supervisor_pending_assign';
    const canAssignSupervisor = (isSupervisorPendingAssign || isSupervisorTask) && (!current.assignedTo || current.assignedTo === '');

    if (!isDesignerAssign && !canAssignSupervisor) {
      return res.status(400).json({ error: 'Assignment allowed only in designer_pending_assign, supervisor_pending_assign or unassigned supervisor tasks' });
    }

    if (isDesignerAssign) {
      // Designer assignment from designer_pending_assign -> checklist
      data.status = 'checklist';
    }

    if (isSupervisorPendingAssign) {
      // Supervisor assignment from supervisor_pending_assign -> floor_protection
      data.status = 'floor_protection';
    }

    // If assigning a supervisor task, require supervisorSelectedPhases
    if (isSupervisorPendingAssign || isSupervisorTask) {
      if (!data.supervisorSelectedPhases || data.supervisorSelectedPhases.length === 0) {
        return res.status(400).json({ error: 'Must provide supervisorSelectedPhases when assigning supervisor task' });
      }
      // Reset phase dates on (re-)assignment
      data.supervisorPhaseDates = {};
    }
  }

  // Due date update permission check - only managers can update due dates
  if (cast.dueDate !== undefined) {
    const role = String(req.headers['x-user-role'] || '').toLowerCase();
    const isManager = role === 'manager';
    if (!isManager) {
      return res.status(403).json({ error: 'Only managers can update due dates' });
    }
  }

  const updated = await prisma.project.update({ where: { id }, data });

  // Sales per-update events: emit diffs for Sales tasks
  const actor = (req.headers['x-user-id'] as string) || null;
  if (isSalesTask(current)) {
    const changes: Array<{ field: string; from: unknown; to: unknown }> = [];
    const trackFields = ['title', 'client', 'salesAmount', 'assignedTo', 'salesSubStatus', 'expiredDate'];
    for (const f of trackFields) {
      const fromVal = (current as Record<string, unknown>)[f];
      const toVal = (updated as Record<string, unknown>)[f];
      if (fromVal !== toVal) {
        changes.push({ field: f, from: fromVal ?? null, to: toVal ?? null });
      }
    }

    if (changes.length > 0) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'sales', type: 'sales_updated', actorUserId: actor, source: 'api', payload: { changes } });
    }

    // Assignment change event for Sales
    if (current.assignedTo !== updated.assignedTo) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'sales', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
    }
  }

  // Designer per-update events: capture general updates and explicit revisions
  if (isDesignerTask(current)) {
    const dChanges: Array<{ field: string; from: unknown; to: unknown }> = [];
    const dTrack = ['title','client','assignedTo','expiredDate','dueDate'];
    for (const f of dTrack) {
      const fromVal = (current as Record<string, unknown>)[f];
      const toVal = (updated as Record<string, unknown>)[f];
      if (fromVal !== toVal) {
        dChanges.push({ field: f, from: fromVal ?? null, to: toVal ?? null });
      }
    }

    if (dChanges.length > 0) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'designer', type: 'designer_updated', actorUserId: actor, source: 'api', payload: { changes: dChanges } });
    }

    if (current.assignedTo !== updated.assignedTo) {
      await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'designer', type: 'assignment_changed', actorUserId: actor, source: 'api', payload: { fromUserId: current.assignedTo ?? null, toUserId: updated.assignedTo ?? null } });
    }
  }

  res.json(updated);
});

router.patch('/:id/status', async (req, res) => {
  const { id } = req.params;
  const parse = updateStatusSchema.safeParse(req.body);
  if (!parse.success) return res.status(400).json({ error: parse.error.flatten() });

  const { status, salesSubStatus, isRevision, isPhaseCompletion } = parse.data;
  let dueDate: string | undefined;
  let revisions3d: string[] | undefined;
  let revisions2d: string[] | undefined;

  const project = await prisma.project.findUnique({ where: { id } });
  const amount = project?.salesAmount ?? 0;

  if (status === '3d' || status === '2d') {
    dueDate = calculateDueDate(status as '3d' | '2d', amount, !!isRevision);
  }

  if (isRevision) {
    const now = new Date().toISOString();
    if (project) {
      if (status === '3d') revisions3d = [ ...(project.revisions3d ?? []), now ];
      if (status === '2d') revisions2d = [ ...(project.revisions2d ?? []), now ];
    }
  }

  const updated = await prisma.project.update({
    where: { id },
    data: {
      status,
      salesSubStatus,
      dueDate: dueDate ? new Date(dueDate) : undefined,
      isRevision: isRevision ?? false,
      ...(revisions3d ? { revisions3d } : {}),
      ...(revisions2d ? { revisions2d } : {}),
    },
  });

  // Emit status change events for Sales/Designer/Supervisor
  const actor = (req.headers['x-user-id'] as string) || null;
  if (project && isSalesTask(project) && (project.status !== updated.status || project.salesSubStatus !== updated.salesSubStatus)) {
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'sales',
      type: 'status_changed',
      actorUserId: actor,
      source: 'api',
      payload: { from: project.status, to: updated.status, salesSubStatusFrom: project.salesSubStatus ?? null, salesSubStatusTo: updated.salesSubStatus ?? null }
    });
  }
  if (project && isDesignerTask(project) && project.status !== updated.status) {
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'designer',
      type: 'status_changed',
      actorUserId: actor,
      source: 'api',
      payload: { from: project.status, to: updated.status }
    });
  }
  if (project && isSupervisorTask(project) && project.status !== updated.status) {
    await emitEvent({
      caseId: updated.caseId,
      taskId: updated.id,
      role: 'supervisor',
      type: 'status_changed',
      actorUserId: actor,
      source: 'api',
      payload: { from: project.status, to: updated.status }
    });
  }

  // Designer explicit revision events
  if (isDesignerTask(project || updated)) {
    if (isRevision) {
      const kind = status === '3d' ? '3d' : status === '2d' ? '2d' : undefined;
      if (kind) {
        await emitEvent({ caseId: updated.caseId, taskId: updated.id, role: 'designer', type: 'designer_revision', actorUserId: actor, source: 'api', payload: { kind } });
      }
    }
  }

  // Supervisor subtask completion tracking

  // Auto-create next role task when rules match
  // Rule 1: Sales reaches won_deal with subStatus '10%' -> create Designer designer_pending_assign
  if (status === 'won_deal' && salesSubStatus === '10%') {
    const caseId = project?.caseId ?? project?.id ?? randomUUID();
    // Avoid duplicate designer task for this source (parentTaskId=Sales task)
    const existingDesigner = await prisma.project.findFirst({ where: { caseId, status: 'designer_pending_assign', parentTaskId: project?.id ?? undefined } });
    if (!existingDesigner) {
      const createdDesigner = await prisma.project.create({
        data: {
          id: randomUUID(),
          title: project?.title || '',
          client: project?.client || '',
          salesAmount: project?.salesAmount ?? 0,
          status: 'designer_pending_assign',
          assignedTo: null,
          caseId,
          parentTaskId: project?.id ?? undefined,
        }
      });
      // Emit task_created for Designer child (not strictly Sales, but keeps timeline coherent)
      await emitEvent({ caseId, taskId: createdDesigner.id, role: 'designer', type: 'task_created', actorUserId: actor, source: 'api', payload: { status: createdDesigner.status } });
    }
  }

  // Rule 1b: Sales reaches won_deal with subStatus '3%'
  // Previously auto-marked as completed here. Change: do NOT auto-complete.
  // Completion should be an explicit action from the client (next click after 3%).

  // Cascade lost_deal to all tasks in the case with role-specific lost deal statuses
  if (status === 'lost_deal' || status === 'designer_lost_deal' || status === 'supervisor_lost_deal') {
    try {
      const allInCase = await prisma.project.findMany({ where: { caseId: updated.caseId } });
      for (const t of allInCase) {
        // Determine the appropriate lost deal status based on task role
        const role: 'sales' | 'designer' | 'supervisor' = isSalesTask(t) ? 'sales' : isDesignerTask(t) ? 'designer' : 'supervisor';
        const lostDealStatus = role === 'sales' ? 'lost_deal' : role === 'designer' ? 'designer_lost_deal' : 'supervisor_lost_deal';

        // Only update if not already in a lost deal state
        if (!['lost_deal', 'designer_lost_deal', 'supervisor_lost_deal'].includes(t.status)) {
          await prisma.project.update({ where: { id: t.id }, data: { status: lostDealStatus } });
          // Emit status_changed for the appropriate role
          await emitEvent({ caseId: t.caseId, taskId: t.id, role, type: 'status_changed', actorUserId: actor, source: 'api', payload: { from: t.status, to: lostDealStatus } });
        }
      }
    } catch (e) {
      // ignore cascade errors to avoid blocking primary update
    }
  }


  // Rule 2: Designer completes -> create Supervisor supervisor_pending_assign (unassigned)
  if (status === 'complete') {
    const caseId = project?.caseId ?? project?.id ?? randomUUID();
    // Avoid duplicate supervisor pending card for this designer task
    const existingSupervisor = await prisma.project.findFirst({ where: { caseId, status: 'supervisor_pending_assign', parentTaskId: project?.id ?? undefined } });
    if (!existingSupervisor) {
      const createdSupervisor = await prisma.project.create({
        data: {
          id: randomUUID(),
          title: project?.title || '',
          client: project?.client || '',
          salesAmount: project?.salesAmount ?? 0,
          status: 'supervisor_pending_assign',
          assignedTo: null,
          caseId,
          parentTaskId: project?.id ?? undefined,
        }
      });
      await emitEvent({ caseId, taskId: createdSupervisor.id, role: 'supervisor', type: 'task_created', actorUserId: actor, source: 'api', payload: { status: createdSupervisor.status } });
    }
  }

  // Supervisor subtask completion tracking
  if (SUPERVISOR_STATUSES.includes(status as (typeof SUPERVISOR_STATUSES)[number]) && req.body.phaseCompletedAt && req.body.phaseKey) {
    const p2 = await prisma.project.findUnique({ where: { id } });
    const phases = ((p2?.supervisorPhaseDates as unknown) || {}) as Record<string, string>;
    phases[req.body.phaseKey] = req.body.phaseCompletedAt;

    // Update subtask completion dates
    await prisma.project.update({ where: { id }, data: { supervisorPhaseDates: phases } });

    // Emit an event for this subtask completion
    await emitEvent({ caseId: p2!.caseId, taskId: p2!.id, role: 'supervisor', type: 'supervisor_task_completed', actorUserId: actor, source: 'api', payload: { phase: req.body.phaseKey, completedAt: req.body.phaseCompletedAt } });

    // If this is a subtask completion event, check if all selected tasks are completed
    if (isPhaseCompletion) {
      const refreshed = await prisma.project.findUnique({ where: { id } });
      const selected = (refreshed?.supervisorSelectedPhases as unknown as string[]) ?? [];
      const allDone = selected.length > 0 && selected.every((s) => (phases as Record<string, string>)[s]);

      if (allDone) {
        const completedProject = await prisma.project.update({ where: { id }, data: { status: 'completed' } });
        return res.json(completedProject);
      }

      return res.json(refreshed);
    }
  }

  res.json(updated);
});

export default router;

