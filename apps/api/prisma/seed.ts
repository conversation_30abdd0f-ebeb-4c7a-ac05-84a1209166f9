import { PrismaClient } from '@prisma/client';
import { randomUUID } from 'crypto';

const prisma = new PrismaClient();

async function main() {
  // Clear existing data
  await prisma.project.deleteMany();

  // Generate UUIDs for projects
  const projectIds = Array.from({ length: 6 }, () => randomUUID());

  // Create sample projects
  const projects = [
    {
      id: projectIds[0],
      caseId: projectIds[0],
      title: 'Modern Office Renovation',
      client: 'Tech Corp Inc.',
      status: 'quotation',
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15'),
      assignedTo: '1', // <PERSON> ID
      salesAmount: 85000
    },
    {
      id: '2',
      caseId: '2',
      title: 'Residential Kitchen Remodel',
      client: 'The Smith Family',
      status: 'won_deal',
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-10'),
      assignedTo: '1', // <PERSON> ID
      salesAmount: 75000,
      salesSubStatus: '5%',
      remarks: 'Client requested premium finishes and extended timeline for quality work.'
    },
    {
      id: '3',
      caseId: '3',
      title: 'Retail Store Buildout',
      client: 'Fashion Boutique LLC',
      status: '3d',
      createdAt: new Date('2024-01-08'),
      updatedAt: new Date('2024-01-08'),
      assignedTo: '2', // Mike Chen ID
      salesAmount: 120000,
      dueDate: new Date('2024-01-20'),
      expiredDate: new Date('2024-01-18'),
      remarks: 'Focus on modern aesthetic with sustainable materials. Client very particular about lighting design.'
    },
    {
      id: '4',
      caseId: '4',
      title: 'Conference Room Upgrade',
      client: 'Law Firm Partners',
      status: 'plaster_ceiling',
      createdAt: new Date('2024-01-05'),
      updatedAt: new Date('2024-01-05'),
      assignedTo: '3', // David Rodriguez ID
      salesAmount: 65000
    },
    {
      id: '5',
      caseId: '5',
      title: 'Warehouse Renovation',
      client: 'Storage Solutions Inc',
      status: 'carpentry_install',
      createdAt: new Date('2024-01-12'),
      updatedAt: new Date('2024-01-12'),
      assignedTo: '4', // Lisa Wang ID (manager)
      salesAmount: 95000
    },
    {
      id: '6',
      caseId: '6',
      title: 'Corporate Office Design',
      client: 'Business Corp Ltd',
      status: 'designer_pending_assign',
      createdAt: new Date('2024-01-14'),
      updatedAt: new Date('2024-01-14'),
      assignedTo: '', // Unassigned pending task
      salesAmount: 105000,
      remarks: 'Rush project - client needs completion by end of month for grand opening.'
    }
  ];

  for (const project of projects) {
    await prisma.project.create({
      data: project
    });
  }

  console.log('Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
